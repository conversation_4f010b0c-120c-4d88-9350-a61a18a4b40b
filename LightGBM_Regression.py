# ==============================================================================
# Part 0: All Imports and Setup
# ==============================================================================
# This section combines all necessary imports from the three original files.


# --- Imports for LightGBM regression ---
import pandas as pd
import numpy as np
import ast
import os
import matplotlib.pyplot as plt
import lightgbm as lgb
from sklearn.preprocessing import StandardScaler, OneHotEncoder

from sklearn.metrics import (
    mean_squared_error, mean_absolute_error, r2_score,
    mean_absolute_percentage_error
)
from sklearn.model_selection import GridSearchCV


# Suppress sklearn warnings
import warnings
warnings.filterwarnings('ignore', category=UserWarning)


# ==============================================================================
# Part 1: Data Splitting Function (from data_prosesser.py)
# ==============================================================================
# This function is now modified to take a DataFrame and return a modified
# DataFrame, instead of reading/writing from/to files.

def split_dataset(df, train_ratio=0.8, test_ratio=0.2, random_state=42):
    """
    Splits the DataFrame into training set and external validation set with 4:1 ratio.
    Training set will be used for 10-fold cross validation.

    Args:
        df (pd.DataFrame): The input DataFrame.
        train_ratio (float): Training set ratio (default: 0.8 for 4:1 split).
        test_ratio (float): External validation set ratio (default: 0.2 for 4:1 split).
        random_state (int): Random seed for reproducibility.

    Returns:
        pd.DataFrame: The DataFrame with an added 'set' column.
    """
    if abs(train_ratio + test_ratio - 1.0) > 1e-6:
        raise ValueError("The sum of ratios must be 1.0")

    # 使用sklearn的train_test_split确保更好的随机性和分层
    from sklearn.model_selection import train_test_split

    df_copy = df.copy()

    # 按4:1比例划分数据集，固定随机种子
    _, external_val_indices = train_test_split(
        range(len(df_copy)),
        test_size=test_ratio,
        random_state=random_state,
        shuffle=True
    )

    # 创建set列标记数据集划分
    df_copy['set'] = 'train'
    df_copy.iloc[external_val_indices, df_copy.columns.get_loc('set')] = 'external_val'

    print("Data successfully split into sets (4:1 ratio):")
    print(df_copy['set'].value_counts(normalize=True))
    print(f"Training set: {(df_copy['set'] == 'train').sum()} samples")
    print(f"External validation set: {(df_copy['set'] == 'external_val').sum()} samples")
    return df_copy




# ==============================================================================
# Part 3: ML Preprocessing and Evaluation Functions (modified for LightGBM regression)
# ==============================================================================

def identify_feature_types(feature_columns, data):
    """
    Identifies numerical, categorical, and mixed features from a list of feature columns.
    Specially handles mixed features like 'Duration'.
    """
    numerical_features = []
    categorical_features = []
    mixed_features = []
    
    for col in feature_columns:
        if col not in data.columns:
            continue
            
        # Try converting to numeric, check for errors
        try:
            # If all values can be converted to numeric, it's a numerical feature
            data[col].astype(float)
            numerical_features.append(col)
        except ValueError:
            # Check if it's a mixed feature with some numeric values
            numeric_mask = pd.to_numeric(data[col], errors='coerce').notna()
            if numeric_mask.any() and not numeric_mask.all():
                mixed_features.append(col)
            else:
                categorical_features.append(col)
    
    print(f"Identified {len(numerical_features)} numerical features: {numerical_features}")
    print(f"Identified {len(categorical_features)} categorical features: {categorical_features}")
    print(f"Identified {len(mixed_features)} mixed features: {mixed_features}")
    
    return numerical_features, categorical_features, mixed_features

def preprocess_data_with_features(data, fingerprint_column, feature_columns):
    """
    Preprocesses data including fingerprints, numerical, categorical, and mixed features.
    """
    data = data.copy()

    # Process fingerprint if present
    if fingerprint_column:
        data[fingerprint_column] = data[fingerprint_column].apply(
            lambda x: ast.literal_eval(x) if isinstance(x, str) else x
        )
        max_length = data[fingerprint_column].apply(len).max()
        data.loc[:, fingerprint_column] = data[fingerprint_column].apply(
            lambda x: x + [0] * (max_length - len(x))
        )
        X_fp = np.array(data[fingerprint_column].tolist())
    else:
        X_fp = np.empty((len(data), 0))
        max_length = 0

    # Identify feature types
    numerical_features, categorical_features, mixed_features = identify_feature_types(feature_columns, data)

    # 创建用于编码的纯分类特征列表（不包含混合特征）
    pure_categorical_features = categorical_features.copy()

    # Process mixed features - create numeric versions and categorical indicators
    mixed_medians = {}  # 存储混合特征的中位数，用于测试集处理
    for mixed_feature in mixed_features:
        # Create numeric version
        numeric_col_name = f"{mixed_feature}_numeric"
        data[numeric_col_name] = pd.to_numeric(data[mixed_feature], errors='coerce')

        # Fill missing values with median
        median_value = data[numeric_col_name].median()
        mixed_medians[mixed_feature] = median_value  # 保存中位数
        data[numeric_col_name].fillna(median_value, inplace=True)

        # Add indicator column
        indicator_col_name = f"{mixed_feature}_is_other"
        data[indicator_col_name] = pd.to_numeric(data[mixed_feature], errors='coerce').isna().astype(int)

        # Add numeric version and indicator to numerical features
        numerical_features.append(numeric_col_name)
        numerical_features.append(indicator_col_name)

        # 将混合特征转换为统一的字符串类型，以便能正确编码
        data[f"{mixed_feature}_str"] = data[mixed_feature].astype(str)
        pure_categorical_features.append(f"{mixed_feature}_str")

    # Process numerical features
    if numerical_features:
        X_num = data[numerical_features].values
    else:
        X_num = np.empty((len(data), 0))

    # Process categorical features
    if pure_categorical_features:
        # One-hot encode each categorical feature
        encoders = {}
        encoded_features = []

        for cat_feature in pure_categorical_features:
            # Fit encoder on this feature
            encoder = OneHotEncoder(sparse_output=False, handle_unknown='ignore')
            feature_values = data[cat_feature].values.reshape(-1, 1)
            encoded_feature = encoder.fit_transform(feature_values)

            # Store encoder for later use
            encoders[cat_feature] = encoder
            encoded_features.append(encoded_feature)

        # Combine all encoded features
        if encoded_features:
            X_cat = np.hstack(encoded_features)
        else:
            X_cat = np.empty((len(data), 0))
    else:
        X_cat = np.empty((len(data), 0))
        encoders = {}

    # Combine fingerprints, numerical and categorical features
    X = np.hstack([X_fp, X_num, X_cat])

    return X, max_length, numerical_features, pure_categorical_features, encoders, mixed_features, mixed_medians

def preprocess_test_with_features(data, fingerprint_column, numerical_features,
                                  categorical_features, max_length, encoders, mixed_features, mixed_medians=None):
    """
    Preprocesses test data using artifacts from training, including mixed features.
    """
    if data.empty:
        return np.array([[]])

    data = data.copy()

    # Process fingerprint if present
    if fingerprint_column:
        data[fingerprint_column] = data[fingerprint_column].apply(
            lambda x: ast.literal_eval(x) if isinstance(x, str) else x
        )
        data.loc[:, fingerprint_column] = data[fingerprint_column].apply(
            lambda x: x + [0] * (max_length - len(x))
        )
        X_fp = np.array(data[fingerprint_column].tolist())
    else:
        X_fp = np.empty((len(data), 0))

    # Process numerical features
    if numerical_features:
        # 创建缺失的数值特征列
        for feat in numerical_features:
            if feat not in data.columns:
                data[feat] = 0
        X_num = data[numerical_features].values
    else:
        X_num = np.empty((len(data), 0))

    # Process mixed features - apply same transformations as training
    for mixed_feature in mixed_features:
        numeric_col_name = f"{mixed_feature}_numeric"
        data[numeric_col_name] = pd.to_numeric(data[mixed_feature], errors='coerce')

        # 使用训练集中保存的中位数填充
        median_value = mixed_medians.get(mixed_feature) if mixed_medians else data[numeric_col_name].median()
        data[numeric_col_name].fillna(median_value, inplace=True)

        indicator_col_name = f"{mixed_feature}_is_other"
        data[indicator_col_name] = pd.to_numeric(data[mixed_feature], errors='coerce').isna().astype(int)

        # 创建字符串版本
        data[f"{mixed_feature}_str"] = data[mixed_feature].astype(str)

    # Process categorical features using stored encoders
    if categorical_features:
        encoded_features = []

        for cat_feature in categorical_features:
            # 确保特征存在
            if cat_feature not in data.columns and cat_feature.endswith('_str'):
                orig_feature = cat_feature.replace('_str', '')
                if orig_feature in data.columns:
                    data[cat_feature] = data[orig_feature].astype(str)

            # Use stored encoder for this feature
            if cat_feature in encoders:
                encoder = encoders[cat_feature]
                feature_values = data[cat_feature].values.reshape(-1, 1)
                encoded_feature = encoder.transform(feature_values)
                encoded_features.append(encoded_feature)

        # Combine all encoded features
        if encoded_features:
            X_cat = np.hstack(encoded_features)
        else:
            X_cat = np.empty((len(data), 0))
    else:
        X_cat = np.empty((len(data), 0))

    # Combine fingerprints, numerical and categorical features
    X = np.hstack([X_fp, X_num, X_cat])

    return X

def evaluate_regression(y_true, y_pred, set_name, output_dir):
    """Evaluates regression model and saves plots."""
    print(f"\n===== {set_name} Evaluation Report =====")

    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_true, y_pred)
    r2 = r2_score(y_true, y_pred)
    mape = mean_absolute_percentage_error(y_true, y_pred)

    print(f"MSE: {mse:.4f}")
    print(f"RMSE: {rmse:.4f}")
    print(f"MAE: {mae:.4f}")
    print(f"R²: {r2:.4f}")
    print(f"MAPE: {mape:.4f}")

    # Create prediction vs actual plot
    plt.figure(figsize=(12, 5))

    plt.subplot(1, 2, 1)
    plt.scatter(y_true, y_pred, alpha=0.6)
    plt.plot([y_true.min(), y_true.max()], [y_true.min(), y_true.max()], 'r--', lw=2)
    plt.xlabel('Actual Values')
    plt.ylabel('Predicted Values')
    plt.title(f'{set_name} - Predicted vs Actual\nR² = {r2:.4f}')

    plt.subplot(1, 2, 2)
    residuals = y_true - y_pred
    plt.scatter(y_pred, residuals, alpha=0.6)
    plt.axhline(y=0, color='r', linestyle='--')
    plt.xlabel('Predicted Values')
    plt.ylabel('Residuals')
    plt.title(f'{set_name} - Residual Plot')

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f'{set_name}_regression_plots_{fp_column}.png'))
    plt.close()

    return {'MSE': mse, 'RMSE': rmse, 'MAE': mae, 'R2': r2, 'MAPE': mape}

def extract_cv_results_from_gridsearch(grid_search, cv_folds=10):
    """从GridSearchCV结果中提取详细的交叉验证指标"""
    print(f"\n===== {cv_folds}-Fold Cross Validation Results from GridSearch =====")

    best_idx = grid_search.best_index_
    cv_results = grid_search.cv_results_

    # 提取最佳模型的交叉验证分数
    r2_scores = []
    for i in range(cv_folds):
        score_key = f'split{i}_test_score'
        if score_key in cv_results:
            r2_scores.append(cv_results[score_key][best_idx])

    r2_scores = np.array(r2_scores)

    print(f"Best parameters: {grid_search.best_params_}")
    print(f"Individual fold R² scores: {[f'{score:.4f}' for score in r2_scores]}")
    print(f"Mean R²: {r2_scores.mean():.4f} ± {r2_scores.std():.4f}")
    print(f"Best CV Score (from GridSearchCV): {grid_search.best_score_:.4f}")

    return {
        'R2_mean': r2_scores.mean(),
        'R2_std': r2_scores.std(),
        'R2_scores': r2_scores,
        'best_score': grid_search.best_score_
    }


# ==============================================================================
# Part 4: Main Execution Block
# ==============================================================================

if __name__ == '__main__':

    # --- 1. Configuration ---
    input_file = 'prepro_DMRepNOEC_fp.xlsx'  # 修改为指定的输入文件
    fp_column = 'Avalon'             # The fingerprint to use for modeling (Mordred, ECFP4, MACCS, Morgan, CMorgan, Avalon, PubChem)
    feature_columns = []                    # Other features to include
    label_column = 'pNOEC_new'                   # The target variable for regression
    output_dir = 'prediction/LightGBM_Regression'  # Directory to save plots and results


    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # --- 2. Data Loading and Preprocessing Pipeline ---
    print("--- Starting QSAR LightGBM Regression Pipeline ---")

    print("\nStep 1: Loading initial data...")
    try:
        df_raw = pd.read_excel(input_file)
        print(f"Data loaded successfully. Shape: {df_raw.shape}")
        print(f"Target variable statistics:")
        print(df_raw[label_column].describe())
    except FileNotFoundError:
        print(f"Error: Input file not found at '{input_file}'")
        exit()

    print("\nStep 2: 读取已有指纹，不再重新计算...")
    # 直接使用输入文件中已有的指纹列（格式形如 "[0.0, 1.0, ...]" 或 list）
    if fp_column not in df_raw.columns:
        print(f"错误: 指定的指纹列 '{fp_column}' 不存在于输入文件中。可用列: {list(df_raw.columns)}")
        exit(1)
    # 不做任何再计算，直接继续
    data_with_fp = df_raw.copy()
    print(f"已读取指纹列 '{fp_column}'，示例前2条：")
    print(data_with_fp[fp_column].head(2))

    print("\nStep 3: Splitting data into training set and external validation set (4:1 ratio)...")
    data = split_dataset(data_with_fp, train_ratio=0.9, test_ratio=0.1, random_state=42)

    print("\n--- Preprocessing and Model Training ---")

    # --- 3. ML Data Preparation ---
    # 分开训练集和外部验证集
    train_data = data[data['set'] == 'train']
    external_val_data = data[data['set'] == 'external_val']

    print(f"Training data set shape: {train_data.shape}")
    print(f"External validation data set shape: {external_val_data.shape}")

    # 处理训练集，包括特征编码
    X_train, max_length, numerical_features, categorical_features, encoders, mixed_features, mixed_medians = preprocess_data_with_features(
        train_data, fp_column, feature_columns
    )

    # 处理外部验证集
    X_external_val = preprocess_test_with_features(
        external_val_data, fp_column, numerical_features, categorical_features, max_length, encoders, mixed_features, mixed_medians
    )

    y_train = train_data[label_column].values
    y_external_val = external_val_data[label_column].values

    # 注意：LightGBM是基于树的模型，通常不需要特征缩放
    # 为了保持代码一致性，这里仍然进行缩放，但可以考虑移除以提高性能
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_external_val_scaled = scaler.transform(X_external_val)

    # --- 4. Model Training and Hyperparameter Tuning on training set ---
    print("\nTraining LightGBM Regressor with 10-fold GridSearchCV on training set...")
    lgb_regressor = lgb.LGBMRegressor(
        random_state=42,
        n_jobs=-1,
        verbosity=-1,  # Suppress LightGBM output
        force_col_wise=True  # Avoid warning for small datasets
    )

    # LightGBM参数网格 - 每个参数都有2个选项
    param_grid = {
        'n_estimators': [100, 200],
        'max_depth': [2, 3, ],
        'learning_rate': [0.01, 0.1],
        'num_leaves': [15],
        'bagging_fraction': [0.7],
        'feature_fraction': [0.7,],
        'reg_alpha': [0.1, ],
        'reg_lambda': [0.0, ]
    }
    # 总组合数：2×2×2×2×2×2×2×2 = 256个组合，2560次拟合

    # 使用10折交叉验证进行超参数调优 - 限制并行进程数以避免内存问题
    grid_search = GridSearchCV(lgb_regressor, param_grid, cv=10, scoring='r2', n_jobs=2, verbose=1)
    grid_search.fit(X_train_scaled, y_train)

    print(f"\nBest parameters found: {grid_search.best_params_}")
    print(f"Best cross-validation R² score: {grid_search.best_score_:.4f}")
    best_model = grid_search.best_estimator_

    # --- 5. 提取并显示10折交叉验证结果 ---
    cv_results = extract_cv_results_from_gridsearch(grid_search, cv_folds=10)

    # --- 6. Model Evaluation ---
    print("\n--- Model Performance Evaluation ---")

    # Training Set Performance
    print("\n1. Training Set Performance:")
    y_train_pred = best_model.predict(X_train_scaled)
    train_metrics = evaluate_regression(y_train, y_train_pred, "Training_Set", output_dir)

    # 10-Fold Cross Validation Performance (already displayed above, but summarize here)
    print(f"\n2. 10-Fold Cross Validation Performance:")
    print(f"   Mean R²: {cv_results['R2_mean']:.4f} ± {cv_results['R2_std']:.4f}")
    print(f"   Best CV Score: {cv_results['best_score']:.4f}")

    # External Validation Set Performance
    print("\n3. External Validation Set Performance:")
    y_external_val_pred = best_model.predict(X_external_val_scaled)
    external_val_metrics = evaluate_regression(y_external_val, y_external_val_pred, "External_Validation", output_dir)

    # --- 7. Final Prediction and Output ---
    print("\nGenerating predictions for the entire dataset...")
    # 处理所有数据用于最终预测
    X_all = preprocess_test_with_features(
        data, fp_column, numerical_features, categorical_features, max_length, encoders, mixed_features, mixed_medians
    )
    X_all_scaled = scaler.transform(X_all)

    data['prediction'] = best_model.predict(X_all_scaled)

    # Calculate residuals for the entire dataset
    data['residuals'] = data[label_column] - data['prediction']

    # 创建用于保存的数据副本，移除分子指纹列以节省文件大小
    data_to_save = data.copy()
    if fp_column in data_to_save.columns:
        data_to_save = data_to_save.drop(columns=[fp_column])
        print(f"注意：为节省文件大小，已从输出文件中移除 {fp_column} 指纹列")

    output_filename = os.path.join(output_dir, f'final_predictions_{fp_column}.xlsx')
    data_to_save.to_excel(output_filename, index=False)

    # Save summary results
    summary_results = {
        'Cross_Validation': cv_results,
        'Training_Metrics': train_metrics,
        'External_Validation_Metrics': external_val_metrics,
        'Best_Parameters': grid_search.best_params_
    }

    # Save summary to text file
    summary_filename = os.path.join(output_dir, f'model_summary_{fp_column}.txt')
    with open(summary_filename, 'w', encoding='utf-8') as f:
        f.write("=== LightGBM Regression Model Summary ===\n\n")
        f.write(f"Input File: {input_file}\n")
        f.write(f"Fingerprint: {fp_column}\n")
        f.write(f"Target Variable: {label_column}\n\n")

        f.write("Best Parameters (from 10-fold GridSearchCV):\n")
        for param, value in grid_search.best_params_.items():
            f.write(f"  {param}: {value}\n")
        f.write("\n")

        f.write("=== Model Performance Results ===\n\n")

        f.write("1. Training Set Metrics:\n")
        for metric, value in train_metrics.items():
            f.write(f"   {metric}: {value:.4f}\n")
        f.write("\n")

        f.write("2. 10-Fold Cross Validation Results:\n")
        f.write(f"   Mean R²: {cv_results['R2_mean']:.4f} ± {cv_results['R2_std']:.4f}\n")
        f.write(f"   Best CV Score: {cv_results['best_score']:.4f}\n")
        f.write(f"   Individual fold R² scores: {[f'{score:.4f}' for score in cv_results['R2_scores']]}\n\n")

        f.write("3. External Validation Set Metrics:\n")
        for metric, value in external_val_metrics.items():
            f.write(f"   {metric}: {value:.4f}\n")
        f.write("\n")

        # 添加特征处理信息
        f.write("Feature Information:\n")
        f.write(f"  Numerical Features: {numerical_features}\n")
        f.write(f"  Categorical Features: {categorical_features}\n")
        for cat_feature in categorical_features:
            if cat_feature in encoders:
                categories = encoders[cat_feature].categories_[0]
                f.write(f"    {cat_feature} Categories: {list(categories)}\n")
        f.write("\n")

    # --- 8. Final Summary ---
    print(f"\n=== FINAL MODEL PERFORMANCE SUMMARY ===")
    print(f"Training Set R²: {train_metrics['R2']:.4f}")
    print(f"10-Fold CV Mean R²: {cv_results['R2_mean']:.4f} ± {cv_results['R2_std']:.4f}")
    print(f"External Validation R²: {external_val_metrics['R2']:.4f}")
    print(f"\nTraining Set RMSE: {train_metrics['RMSE']:.4f}")
    print(f"External Validation RMSE: {external_val_metrics['RMSE']:.4f}")

    print(f"\n--- LightGBM Pipeline Finished ---")
    print(f"Plots and final predictions saved in '{output_dir}' directory.")
    print(f"Final output file: '{output_filename}'")
    print(f"Model summary: '{summary_filename}'")
