=== XGBoost Regression Model Summary ===

Input File: prepro_DMRepNOEC_fp.xlsx
Fingerprint: Mordred
Target Variable: pNOEC_new

Best Parameters:
  alpha: 0.5
  colsample_bytree: 0.6
  gamma: 1
  lambda: 10
  learning_rate: 0.1
  max_depth: 3
  n_estimators: 150
  subsample: 0.6

=== Model Performance Results ===

1. Training Set Metrics:
   MSE: 0.4244
   RMSE: 0.6515
   MAE: 0.4868
   R2: 0.8073
   MAPE: 0.0937

2. 10-Fold Cross Validation Results:
   Mean R²: 0.1366 ± 0.6697
   Best CV Score: 0.1366
   Individual fold R² scores: ['0.5700', '0.6818', '0.4919', '0.4383', '0.4961', '-0.0081', '0.1124', '0.2329', '0.1170', '-1.7665']

3. External Validation Set Metrics:
   MSE: 1.2919
   RMSE: 1.1366
   MAE: 0.8458
   R2: 0.4616
   MAPE: 0.1685

Feature Information:
  Numerical Features: []
  Categorical Features: []

