=== XGBoost Regression Model Summary ===

Input File: prepro_DMRepNOEC_fp.xlsx
Fingerprint: MACCS
Target Variable: pNOEC_new

Best Parameters:
  alpha: 0.5
  colsample_bytree: 0.7
  gamma: 1
  lambda: 1
  learning_rate: 0.1
  max_depth: 3
  n_estimators: 150
  subsample: 0.7

=== Model Performance Results ===

1. Training Set Metrics:
   MSE: 0.7742
   RMSE: 0.8799
   MAE: 0.6777
   R2: 0.6485
   MAPE: 0.1310

2. 10-Fold Cross Validation Results:
   Mean R²: 0.0534 ± 0.7062
   Best CV Score: 0.0534
   Individual fold R² scores: ['0.4894', '0.6108', '0.4001', '0.4067', '0.3987', '-0.1929', '0.1976', '0.0209', '0.1569', '-1.9538']

3. External Validation Set Metrics:
   MSE: 1.3112
   RMSE: 1.1451
   MAE: 0.8617
   R2: 0.4536
   MAPE: 0.1745

Feature Information:
  Numerical Features: []
  Categorical Features: []

