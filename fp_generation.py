from rdkit import RDLogger
# 获取 RDLogger 默认的日志记录器
lg = RDLogger.logger()
# 将日志级别设置为 RDLogger.CRITICAL，只显示最关键的错误
lg.setLevel(RDLogger.CRITICAL)


import pandas as pd
from rdkit import Chem
from rdkit.Chem import AllChem, MACCSkeys
from rdkit.Avalon import pyAvalonTools
from rdkit.DataStructs import ExplicitBitVect
from mordred import Calculator, descriptors
from mordred.error import Error
from tqdm import tqdm
import numpy as np
from skfp.fingerprints import ECFPFingerprint, PubChemFingerprint

# 定义一个函数，获取“手动映射”的 count-based Morgan指纹
def get_count_based_morgan_fp(mol, radius=2, nBits=2048):
    """
    使用 AllChem.GetMorganFingerprint(mol, radius=..., useCounts=True) 获得“非哈希”的稀疏指纹,
    然后手动映射到一个固定长度 (nBits) 的数组, 从而得到 count-based hashed 指纹.
    """
    if mol is None:
        return [0]*nBits

    # 1) 获取带计数的非哈希Morgan指纹（SparseIntVect）
    fp_sparse = AllChem.GetMorganFingerprint(mol, radius=radius, useCounts=True)
    # 2) 转成 featureID->count 的字典
    nonzero_dict = fp_sparse.GetNonzeroElements()

    # 3) 分配一个固定大小的数组来存放计数
    count_array = np.zeros(nBits, dtype=int)

    # 4) 手动哈希: 取 featureID % nBits 作为下标, 累加出现次数
    for feature_id, count_value in nonzero_dict.items():
        idx = feature_id % nBits
        count_array[idx] += count_value

    return count_array.tolist()

# 定义一个函数，生成所有指纹/描述符
def generate_fingerprints(smiles):
    molecule = Chem.MolFromSmiles(smiles)
    results = {}

    if molecule:
        # (1) Mordred 描述符
        calc = Calculator(descriptors, ignore_3D=True)
        try:
            mordred_result = calc(molecule)
            results['Mordred'] = mordred_result.asdict()
        except Exception as e:
            print(f"Error calculating Mordred descriptors for {smiles}: {e}")
            results['Mordred'] = {}

        # (2) ECFP (bit-based Morgan, radius=2)
        try:
            ecfp = AllChem.GetMorganFingerprintAsBitVect(molecule, 2, nBits=2048)
            results['ECFP4'] = list(map(int, ecfp.ToBitString()))
        except Exception as e:
            print(f"Error calculating ECFP for {smiles}: {e}")
            results['ECFP4'] = []

        # (3) MACCS
        try:
            maccs = MACCSkeys.GenMACCSKeys(molecule)
            results['MACCS'] = list(map(int, maccs.ToBitString()))
        except Exception as e:
            print(f"Error calculating MACCS for {smiles}: {e}")
            results['MACCS'] = []

        # (4) bit-based Morgan (同 ECFP, 只是命名区别, 你可以不重复)
        #     如果你已经用 ECFP，就可以省略这一步。
        try:
            bit_morgan = AllChem.GetMorganFingerprintAsBitVect(molecule, 1, nBits=2048)
            results['Morgan'] = list(map(int, bit_morgan.ToBitString()))
        except Exception as e:
            print(f"Error calculating bit-based Morgan for {smiles}: {e}")
            results['Morgan'] = []

        # (5) count-based Morgan (手动映射)
        try:
            c_morgan = get_count_based_morgan_fp(molecule, radius=2, nBits=2048)
            results['CMorgan'] = c_morgan
        except Exception as e:
            print(f"Error calculating count-based Morgan for {smiles}: {e}")
            results['CMorgan'] = []
        # (6) Avalon指纹
        try:
            avalon_fp = pyAvalonTools.GetAvalonFP(molecule, nBits=2048)  # 这里使用2048位长度
            results['Avalon'] = list(map(int, avalon_fp.ToBitString()))
        except Exception as e:
            print(f"Error calculating Avalon for {smiles}: {e}")
            results['Avalon'] = []
        # (7) PubChem指纹
        try:
            pubchem_fp_obj = PubChemFingerprint(n_jobs=-1)
            results['PubChem'] = pubchem_fp_obj.transform([smiles])[0].tolist()
        except Exception as e:
            print(f"Error calculating PubChem for {smiles}: {e}")
            results['PubChem'] = []
    else:
        # SMILES 解析失败时，返回空
        results = {
            'Mordred': {}, 
            'ECFP4': [], 
            'MACCS': [],
            'Morgan': [],
            'CMorgan': [],
            'Avalon': [],   
            'PubChem': []
        }

    return results

# 主流程：读取Excel, 计算指纹, 存回Excel
df = pd.read_excel('prepro_DMRepNOEC.xlsx')

all_descriptors = []
for smiles in tqdm(df['Canonical smiles'], desc='Generating All Fingerprints'):
    all_descriptors.append(generate_fingerprints(smiles))

# 拆分成各自的列表
mordred_list = [d['Mordred'] for d in all_descriptors]
ecfp_list = [d['ECFP4'] for d in all_descriptors]
maccs_list = [d['MACCS'] for d in all_descriptors]
morgan_list = [d['Morgan'] for d in all_descriptors]
cmorgan_list = [d['CMorgan'] for d in all_descriptors]
avalon_list = [d['Avalon'] for d in all_descriptors]
pubchem_list = [d['PubChem'] for d in all_descriptors]

# ========== 处理 Mordred 描述符 ==========
modred_df = pd.DataFrame(mordred_list)

# 删除含NaN列
modred_df = modred_df.dropna(axis=1)
# 仅保留数值型
modred_df = modred_df.select_dtypes(include=[np.number])
# 去掉异常 (mordred.error.Error) 值
from mordred.error import Error
modred_df = modred_df.loc[:, ~modred_df.applymap(lambda x: isinstance(x, Error)).any()]

# 去除相关性大于0.9的列
corr_matrix = modred_df.corr().abs()
upper_triangle = corr_matrix.where(np.triu(np.ones(corr_matrix.shape), k=1).astype(bool))
to_drop = [col for col in upper_triangle.columns if any(upper_triangle[col] > 0.9)]
modred_df = modred_df.drop(columns=to_drop)

# ========== 指纹列表转换为DataFrame ==========
ecfp_df = pd.DataFrame(ecfp_list)
maccs_df = pd.DataFrame(maccs_list)
morgan_df = pd.DataFrame(morgan_list)
cmorgan_df = pd.DataFrame(cmorgan_list)
avalon_df = pd.DataFrame(avalon_list)

# 设置列名前缀
ecfp_df.columns = [f'ECFP4_{i}' for i in range(ecfp_df.shape[1])]
maccs_df.columns = [f'MACCS_{i}' for i in range(maccs_df.shape[1])]
morgan_df.columns = [f'Morgan_{i}' for i in range(morgan_df.shape[1])]
cmorgan_df.columns = [f'MorganCount_{i}' for i in range(cmorgan_df.shape[1])]
modred_df.columns = [f'Mordred_{c}' for c in modred_df.columns]
avalon_df.columns = [f'Avalon_{i}' for i in range(avalon_df.shape[1])]
pubchem_df = pd.DataFrame(pubchem_list)
pubchem_df.columns = [f'PubChem_{i}' for i in range(pubchem_df.shape[1])]

# 打印信息
print(f"Mordred描述符数量: {modred_df.shape[1]}")
print(f"ECFP4指纹数量: {ecfp_df.shape[1]}")
print(f"MACCS指纹数量: {maccs_df.shape[1]}")
print(f"Morgan指纹数量: {morgan_df.shape[1]}")
print(f"CMorgan指纹数量: {cmorgan_df.shape[1]} (count-based)")
print(f"Avalon指纹数量: {avalon_df.shape[1]}")
print(f"PubChem指纹数量: {pubchem_df.shape[1]}")

# 将结果写回 df
df['Mordred'] = modred_df.values.tolist()
df['ECFP4'] = ecfp_df.values.tolist()
df['MACCS'] = maccs_df.values.tolist()
df['Morgan'] = morgan_df.values.tolist()
df['CMorgan'] = cmorgan_df.values.tolist()
df['Avalon'] = avalon_df.values.tolist()
df['PubChem'] = pubchem_df.values.tolist()

df.to_excel('prepro_DMRepNOEC_fp.xlsx', index=False)
