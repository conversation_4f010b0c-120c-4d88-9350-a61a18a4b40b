=== XGBoost Regression Model Summary ===

Input File: prepro_DMRepNOEC_fp.xlsx
Fingerprint: PubChem
Target Variable: pNOEC_new

Best Parameters:
  alpha: 0.5
  colsample_bytree: 0.8
  gamma: 1
  lambda: 10
  learning_rate: 0.1
  max_depth: 3
  n_estimators: 200
  subsample: 0.6

=== Model Performance Results ===

1. Training Set Metrics:
   MSE: 0.7599
   RMSE: 0.8717
   MAE: 0.6631
   R2: 0.6550
   MAPE: 0.1273

2. 10-Fold Cross Validation Results:
   Mean R²: 0.0734 ± 0.7379
   Best CV Score: 0.0734
   Individual fold R² scores: ['0.5664', '0.6319', '0.4408', '0.4366', '0.4111', '0.0170', '0.1195', '0.1075', '0.0521', '-2.0486']

3. External Validation Set Metrics:
   MSE: 1.3193
   RMSE: 1.1486
   MAE: 0.8440
   R2: 0.4502
   MAPE: 0.1648

Feature Information:
  Numerical Features: []
  Categorical Features: []

