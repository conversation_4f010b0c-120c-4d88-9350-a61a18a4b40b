=== XGBoost Regression Model Summary ===

Input File: prepro_DMRepNOEC_fp.xlsx
Fingerprint: Avalon
Target Variable: pNOEC_new

Best Parameters:
  alpha: 0.5
  colsample_bytree: 0.7
  gamma: 1
  lambda: 10
  learning_rate: 0.1
  max_depth: 3
  n_estimators: 150
  subsample: 0.7

=== Model Performance Results ===

1. Training Set Metrics:
   MSE: 0.7593
   RMSE: 0.8714
   MAE: 0.6767
   R2: 0.6553
   MAPE: 0.1317

2. 10-Fold Cross Validation Results:
   Mean R²: -0.0086 ± 0.9444
   Best CV Score: -0.0086
   Individual fold R² scores: ['0.3816', '0.6056', '0.4233', '0.3988', '0.3924', '0.0940', '0.2844', '0.0507', '0.0790', '-2.7959']

3. External Validation Set Metrics:
   MSE: 1.3556
   RMSE: 1.1643
   MAE: 0.8798
   R2: 0.4351
   MAPE: 0.1784

Feature Information:
  Numerical Features: []
  Categorical Features: []

